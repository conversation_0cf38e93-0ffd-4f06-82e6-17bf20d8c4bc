# 🚀 CloudQX - Multi-Cloud Infrastructure Management Tool

CloudQX is a comprehensive CLI tool for managing and analyzing cloud infrastructure across multiple cloud providers, with robust support for Microsoft Azure including both Global Azure and Azure China Cloud.

## ✨ Features

### 🌐 Multi-Cloud Support
- **Azure Provider**: Full support for Azure Global Cloud and Azure China Cloud with dual authentication
- **Extensible Architecture**: Designed to be extended with AWS, GCP, and other cloud providers
- **Unified CLI**: Single command-line interface across all cloud providers

### 📊 Rich Reporting & Analytics
- **HTML Reports**: Interactive HTML reports with embedded CSS/JS for portability
- **Organization Hierarchy**: Complete Azure tenant, management group, and subscription analysis
- **Resource Overview**: Comprehensive resource inventory across all Azure services
- **Network Analysis**: Virtual networks, subnets, DNS, and connectivity insights
- **User & RBAC**: Identity management and role-based access control analysis
- **Private DNS**: DNS zone management and record analysis

### 🔧 Advanced Capabilities
- **Dual Authentication**: Support for both `DefaultAzureCredential` and `ChinaAzureCliCredential`
- **Token Caching**: Efficient credential management with singleton pattern
- **Background Processing**: Async operations for large-scale resource analysis
- **Rich Console Output**: Beautiful terminal output using `rich` and `tabulate`
- **Debug Mode**: Comprehensive logging and troubleshooting capabilities

## 🚀 Installation

### Prerequisites
- Python 3.8+ 
- Azure CLI (for Azure authentication)
- Active Azure subscription with appropriate permissions

### Setup

1. **Clone the repository:**
   ```bash
   git clone https://github.com/your-username/cloudqx.git
   cd cloudqx
   ```

2. **Create and activate virtual environment:**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Authenticate with Azure:**
   ```bash
   # For Global Azure
   az login
   
   # For Azure China Cloud
   az login --cloud AzureChinaCloud
   ```

## 💻 Usage

The main entry point is `cloudqx.py` with support for multiple Azure modules.

### 🔍 Azure Commands

CloudQX provides comprehensive Azure management capabilities through specialized modules:

#### 🏢 Organization Analysis
Analyze Azure tenant hierarchy, management groups, and subscription structure:

```bash
python cloudqx.py azure organization [OPTIONS]
```

**Options:**
- `--debug, -d`: Enable debug logging
- `--cloud, -c`: Cloud environment (`global` or `china`)

#### 👥 User & RBAC Management
Comprehensive user identity and role-based access control analysis:

```bash
python cloudqx.py azure user [OPTIONS]
```

#### 🌐 Virtual Network Analysis
Analyze VNets, subnets, peering, and network connectivity:

```bash
python cloudqx.py azure vnet [OPTIONS]
```

#### 🔒 Private DNS Management
DNS zone management and private DNS record analysis:

```bash
python cloudqx.py azure dns-private [OPTIONS]
```

#### 📋 Resource Overview
Comprehensive inventory of all Azure resources across subscriptions:

```bash
python cloudqx.py azure resource-overview [OPTIONS]
```

### 🌍 Cloud Environment Support

**Global Azure:**
```bash
python cloudqx.py azure organization --cloud global --debug
```

**Azure China Cloud:**
```bash
python cloudqx.py azure organization --cloud china --debug
```

### 📄 HTML Report Generation

All commands generate interactive HTML reports in the `html/` directory:

```bash
# Generate comprehensive reports for all modules
./scripts/generate_reports.sh

# Reports are saved as:
# html/azure_organization_report_{cloud}.html
# html/azure_user_report_{cloud}.html
# html/azure_vnet_report_{cloud}.html
# html/azure_dns_private_report_{cloud}.html
# html/azure_resource_overview_report_{cloud}.html
```

### 🛠️ Direct Module Execution

You can also run provider modules directly:

```bash
# Direct Azure module execution
python -m providers.azure organization
python -m providers.azure user
python -m providers.azure vnet
python -m providers.azure dns-private
python -m providers.azure resource-overview
```

## 🏗️ Development

### Architecture Overview

CloudQX follows a **Provider Pattern** architecture for multi-cloud support:

```
cloudqx/
├── cloudqx.py                     # Main CLI entry point
├── providers/
│   └── azure/                     # Azure provider implementation
│       ├── __main__.py           # Module router and entry point
│       ├── organization.py       # Organization hierarchy analysis
│       ├── user.py              # User and RBAC management
│       ├── vnet.py              # Virtual network analysis  
│       ├── dns_private.py       # Private DNS management
│       ├── resource_overview.py # Resource inventory
│       └── lib/                 # Shared libraries
│           ├── common/          # Auth, config, token management
│           ├── organization/    # Organization-specific logic
│           ├── user/           # User-specific logic
│           ├── vnet/           # Network-specific logic
│           ├── dns_private/    # DNS-specific logic
│           └── resource_overview/ # Resource-specific logic
├── html/                        # Generated HTML reports
├── tests/                      # Comprehensive test suite
└── scripts/                    # Utility scripts
```

### 🔑 Key Design Patterns

- **Dual Authentication**: Supports both Global Azure and Azure China Cloud
- **Token Caching**: Singleton credential management for performance
- **HTML-First Output**: Interactive reports with embedded CSS/JS
- **Module Entry Points**: Direct execution via `python -m providers.azure {module}`
- **Error Handling**: User-friendly Azure SDK exception handling

### 🧪 Testing

```bash
# Run all tests
pytest

# Run specific test files
pytest tests/test_auth.py
pytest tests/test_azure_resources.py

# Run with coverage
pytest --cov=providers tests/
```

### 📊 Report Development

HTML reports use vanilla JavaScript and embedded CSS for portability:

- **Class-based Architecture**: ES6 classes for report managers
- **Hash Navigation**: Client-side routing for single-page apps
- **Interactive Tables**: Search, sort, and filter capabilities
- **Self-contained**: No external dependencies or CDNs

## 🔐 Security & Best Practices

### Authentication
- **Azure Credentials**: Uses `DefaultAzureCredential` with proper authority hosts
- **Token Security**: Secure caching via `lib/common/token_manager.py` 
- **No Hardcoded Secrets**: Relies on Azure CLI or managed identity
- **Input Validation**: Validates cloud environment parameters (`global`, `china`)

### Data Privacy
- **PII Protection**: Redacts sensitive information from logs by default
- **Secure Queries**: Uses parameterized queries with Azure Resource Graph
- **Privacy-First**: Default to privacy-preserving data handling

## 🤝 Contributing

We welcome contributions! Please follow these guidelines:

1. **Fork the repository** and create a feature branch
2. **Follow the established patterns** in `lib/common/` for authentication and configuration
3. **Add tests** for new functionality in the `tests/` directory
4. **Update documentation** including this README for new features
5. **Submit a pull request** with a clear description of changes

### Development Setup

```bash
# Install development dependencies
pip install -r requirements.txt

# Run tests before committing
pytest

# Check code style
flake8 providers/ tests/

# Generate test reports
./scripts/generate_reports.sh
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Azure SDK for Python** - Core Azure integration
- **Typer** - Beautiful CLI framework
- **Rich** - Enhanced console output
- **Microsoft Graph** - Azure AD and identity management

---

**CloudQX Team** - Building the future of multi-cloud infrastructure management 🚀

<!-- Contains AI-generated edits. -->

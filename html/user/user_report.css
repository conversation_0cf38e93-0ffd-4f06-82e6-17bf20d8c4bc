body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    max-width: 1800px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #0078d4, #106ebe);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    margin: 0;
    font-size: 2.5em;
    font-weight: 300;
}

.header p {
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-size: 1.1em;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    padding: 30px;
    background: #fafafa;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #0078d4;
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #0078d4;
    margin: 0;
}

.stat-label {
    color: #666;
    margin: 5px 0 0 0;
    font-size: 0.9em;
}

.table-container {
    padding: 30px;
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th {
    background: #0078d4;
    color: white;
    padding: 15px 10px;
    text-align: left;
    font-weight: 500;
    cursor: pointer;
    position: relative;
    user-select: none;
    transition: background-color 0.2s;
}

th:hover {
    background: #106ebe;
}

th.sortable::after {
    content: ' ↕';
    opacity: 0.5;
    font-size: 0.8em;
    margin-left: 5px;
}

th.sort-asc::after {
    content: ' ↑';
    opacity: 1;
}

th.sort-desc::after {
    content: ' ↓';
    opacity: 1;
}

td {
    padding: 15px 10px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

tr:hover {
    background-color: #f8f9fa;
}

.user-info strong {
    color: #333;
    font-size: 1.1em;
}

.text-muted {
    color: #666;
    font-size: 0.9em;
}

.role-badge {
    display: inline-block;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
}

.entra-role {
    background-color: #e3f2fd;
    color: #1565c0;
    border: 1px solid #bbdefb;
}

.rbac-role {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #e1bee7;
}

.role-scope {
    font-size: 0.75em;
    color: #666;
    font-style: italic;
}

.rbac-scope {
    font-size: 0.75em;
    color: #666;
    font-weight: normal;
    line-height: 1.2;
    margin-left: 8px;
}

.rbac-scope small {
    color: #777;
    font-size: 0.9em;
}

.rbac-role-item {
    margin-bottom: 3px;
    padding: 1px 0;
    line-height: 1.3;
}

.rbac-role-item:last-child {
    margin-bottom: 0;
}

.no-roles {
    color: #999;
    font-style: italic;
}

.footer {
    padding: 20px 30px;
    background: #f8f9fa;
    text-align: center;
    color: #666;
    border-top: 1px solid #eee;
}

.search-box {
    margin-bottom: 20px;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    width: 100%;
    max-width: 400px;
}

.filter-buttons {
    margin-bottom: 20px;
}

.filter-btn {
    padding: 8px 16px;
    margin: 0 5px 5px 0;
    border: 2px solid #0078d4;
    background: white;
    color: #0078d4;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.filter-btn.active, .filter-btn:hover {
    background: #0078d4;
    color: white;
}

.rbac-role-group {
    margin-bottom: 12px;
    padding: 8px;
    background-color: #fafafa;
    border-left: 3px solid #7b1fa2;
    border-radius: 4px;
}

.rbac-role-group:last-child {
    margin-bottom: 0;
}

.rbac-role-name {
    margin-bottom: 6px;
}

.rbac-resource-type {
    margin-left: 12px;
    margin-bottom: 4px;
    padding: 4px 0;
    border-left: 2px solid #ddd;
    padding-left: 8px;
}

.resource-type-badge {
    display: inline-block;
    padding: 2px 6px;
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
    border-radius: 3px;
    font-size: 0.75em;
    font-weight: 500;
    margin-right: 4px;
}

.scope-count {
    font-size: 0.7em;
    color: #666;
    font-weight: normal;
}

.scope-item {
    margin-left: 8px;
    font-size: 0.75em;
    color: #555;
    line-height: 1.3;
    padding: 1px 0;
    word-break: break-word;
    hyphens: auto;
}

.more-scopes {
    color: #888;
    font-style: italic;
}

.expand-toggle {
    margin-left: 8px;
    margin-top: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #0078d4;
    font-size: 0.75em;
    user-select: none;
    transition: color 0.2s;
}

.expand-toggle:hover {
    color: #106ebe;
}

.expand-arrow {
    margin-right: 4px;
    font-size: 0.8em;
    transition: transform 0.2s;
}

.expand-arrow.expanded {
    transform: rotate(90deg);
}

.expand-text {
    font-weight: 500;
}

.expandable-scopes {
    margin-left: 8px;
    margin-top: 2px;
    overflow: hidden;
    transition: all 0.3s ease;
}

@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 0;
    }
    
    .header {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        padding: 20px;
    }
    
    .table-container {
        padding: 20px 10px;
    }
    
    th, td {
        padding: 10px 5px;
        font-size: 0.9em;
    }
}

/* Contains AI-generated edits. */

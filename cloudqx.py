#!/usr/bin/env python3
"""
CloudQX - Multi-Cloud Infrastructure Management Tool

A unified CLI tool for managing and analyzing cloud infrastructure across
multiple cloud providers including Azure, AWS, GCP, and more.

Usage:
    python cloudqx.py azure user [OPTIONS]
    python cloudqx.py --help
    
Author: CloudQX Team
License: MIT
"""

import sys
import os
import subprocess
from pathlib import Path
from typing import Optional
import logging

import typer
from typing_extensions import Annotated

# Version information
__version__ = "1.1.0"
__author__ = "CloudQX Team"

# Configure logging to include filename and line number
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
)

# Add the project root to Python path for imports
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Main app
app = typer.Typer(
    name="cloudqx",
    help="CloudQX - Multi-Cloud Infrastructure Management Tool",
    no_args_is_help=True,
    pretty_exceptions_enable=False,
    add_completion=True,
    rich_markup_mode="rich",
    context_settings={"help_option_names": ["-h", "--help"]},
)

# Azure sub-app
azure_app = typer.Typer(
    name="azure",
    help="Azure cloud provider commands and utilities",
    no_args_is_help=True,
    rich_markup_mode="rich",
    context_settings={"help_option_names": ["-h", "--help"]},
)

# AWS sub-app (placeholder for future)
aws_app = typer.Typer(
    name="aws",
    help="AWS cloud provider commands and utilities",
    no_args_is_help=True,
    rich_markup_mode="rich",
    context_settings={"help_option_names": ["-h", "--help"]},
)

# GCP sub-app (placeholder for future)
gcp_app = typer.Typer(
    name="gcp",
    help="Google Cloud Platform commands and utilities",
    no_args_is_help=True,
    rich_markup_mode="rich",
    context_settings={"help_option_names": ["-h", "--help"]},
)

# Alibaba sub-app
alibaba_app = typer.Typer(
    name="alibaba",
    help="Alibaba Cloud provider commands and utilities",
    no_args_is_help=True,
    rich_markup_mode="rich",
    context_settings={"help_option_names": ["-h", "--help"]},
)

# Add sub-apps to main app
app.add_typer(azure_app, name="azure")
app.add_typer(aws_app, name="aws")
app.add_typer(gcp_app, name="gcp")
app.add_typer(alibaba_app, name="alibaba")


@azure_app.command("user")
def azure_user(
    debug: Annotated[
        bool, 
        typer.Option(
            "--debug", "-d", 
            help="Enable debug logging to show detailed debugging information"
        )
    ] = False,
    cloud: Annotated[
        Optional[str], 
        typer.Option(
            "--cloud", "-c",
            help="Specify Azure cloud environment (auto-detects if not specified)",
            case_sensitive=False
        )
    ] = None,
    web: Annotated[
        bool,
        typer.Option(
            "--web", "-w",
            help="Launch HTTP server and open HTML report in web browser (default: off)"
        )
    ] = False,
):
    """
    Retrieve and analyze Azure users with Entra ID and RBAC roles.
    
    By default, generates an HTML report and opens it in the browser.
    This tool works with both Global Azure and China Azure environments.
    
    Examples:
        cloudqx azure user                     # Generate HTML report (auto-detect cloud)
        cloudqx azure user --debug             # Generate HTML report with debug logging
        cloudqx azure user --cloud global      # Force use of Global Azure
        cloudqx azure user --cloud china       # Force use of China Azure
        cloudqx azure user --web               # Generate report and launch in browser
    """
    try:
        # Validate cloud parameter
        if cloud is not None and (not cloud or cloud.lower() not in ['global', 'china']):
            typer.echo(f"Invalid cloud environment '{cloud}'. Must be 'global' or 'china'.")
            raise typer.Exit(1)
        
        # Run the Azure user script as a subprocess
        
        # Build command (invoke as module to ensure package imports work)
        cmd = [sys.executable, "-m", "providers.azure.user"]
        
        if debug:
            cmd.append("--debug")
        if cloud:
            cmd.extend(["--cloud", cloud.lower()])
        if web:
            cmd.append("--web")
        
        # Run the command with proper error handling
        try:
            result = subprocess.run(
                cmd, 
                cwd=str(project_root),
                check=False,  # Don't raise exception on non-zero exit
                capture_output=False  # Let subprocess output go directly to terminal
            )
            
            # Exit with the same code as the subprocess
            if result.returncode != 0:
                raise typer.Exit(result.returncode)
                
        except subprocess.TimeoutExpired as e:
            typer.echo(f"Error running Azure user command: {str(e)}")
            raise typer.Exit(1)
        except subprocess.SubprocessError as e:
            typer.echo(f"Error running Azure user command: {str(e)}")
            raise typer.Exit(1)
        except KeyboardInterrupt:
            typer.echo("Operation cancelled by user.", err=True)
            raise typer.Exit(1)
        except FileNotFoundError:
            typer.echo(
                f"Error: Azure user script not found at {project_root / 'providers' / 'azure' / 'user.py'}",
                err=True
            )
            raise typer.Exit(1)
            
    except KeyboardInterrupt:
        typer.echo("Operation cancelled by user.", err=True)
        raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"Error: {str(e)}", err=True)
        if debug:
            import traceback
            typer.echo(f"Traceback: {traceback.format_exc()}", err=True)
        raise typer.Exit(1)


@azure_app.command("organization")
def azure_organization(
    debug: Annotated[
        bool, 
        typer.Option(
            "--debug", "-d", 
            help="Enable debug logging to show detailed debugging information"
        )
    ] = False,
    cloud: Annotated[
        Optional[str], 
        typer.Option(
            "--cloud", "-c",
            help="Specify Azure cloud environment (auto-detects if not specified)",
            case_sensitive=False
        )
    ] = None,
    include_resources: Annotated[
        bool,
        typer.Option(
            "--include-resources", "-r",
            help="Include all resources within each resource group (enhances organization view with detailed resource information)"
        )
    ] = False,
    full_list: Annotated[
        bool,
        typer.Option(
            "--full-list", "-f",
            help="Show full list of resources when used with --include-resources (displays complete resource inventory)"
        )
    ] = False,
    web: Annotated[
        bool,
        typer.Option(
            "--web", "-w",
            help="Launch HTTP server and open HTML report in web browser (default: off)"
        )
    ] = False,
):
    """
    Show the Azure hierarchy of Management Groups, Subscriptions, and Resource Groups.
    
    Examples:
        cloudqx azure organization
        cloudqx azure organization --debug
        cloudqx azure organization --include-resources
        cloudqx azure organization --include-resources --full-list
        cloudqx azure organization --include-resources --full-list --debug
        cloudqx azure organization --web               # Generate report and launch in browser
    """
    try:
        # Validate cloud parameter
        if cloud is not None and (not cloud or cloud.lower() not in ['global', 'china']):
            typer.echo(f"Invalid cloud environment '{cloud}'. Must be 'global' or 'china'.")
            raise typer.Exit(1)
        
        # Run the Azure organization script as a subprocess
        
        # Build command (invoke as module to ensure package imports work)
        cmd = [sys.executable, "-m", "providers.azure.organization"]
        
        if debug:
            cmd.append("--debug")
        if cloud:
            cmd.extend(["--cloud", cloud.lower()])
        if include_resources:
            cmd.append("--include-resources")
        if full_list:
            cmd.append("--full-list")
        if web:
            cmd.append("--web")
        
        # Run the command
        try:
            result = subprocess.run(
                cmd, 
                cwd=str(project_root),
                check=False,
                capture_output=False
            )
            
            if result.returncode != 0:
                raise typer.Exit(result.returncode)
                
        except KeyboardInterrupt:
            typer.echo("Operation cancelled by user.", err=True)
            raise typer.Exit(1)
        except subprocess.SubprocessError as e:
            typer.echo(f"Error running Azure organization command: {str(e)}", err=True)
            raise typer.Exit(1)
        except FileNotFoundError:
            typer.echo(
                f"Error: Azure organization script not found at {project_root / 'providers' / 'azure' / 'organization.py'}",
                err=True
            )
            raise typer.Exit(1)
            
    except KeyboardInterrupt:
        typer.echo("Operation cancelled by user.", err=True)
        raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"Error: {str(e)}", err=True)
        if debug:
            import traceback
            typer.echo(f"Traceback: {traceback.format_exc()}", err=True)
        raise typer.Exit(1)


@azure_app.command("vnet")
def azure_vnet(
    debug: Annotated[
        bool, 
        typer.Option(
            "--debug", "-d", 
            help="Enable debug logging to show detailed debugging information"
        )
    ] = False,
    cloud: Annotated[
        Optional[str], 
        typer.Option(
            "--cloud", "-c",
            help="Specify Azure cloud environment (auto-detects if not specified)",
            case_sensitive=False
        )
    ] = None,
    web: Annotated[
        bool,
        typer.Option(
            "--web", "-w",
            help="Launch HTTP server and open HTML report in web browser (default: off)"
        )
    ] = False,
):
    """
    Retrieve and analyze Azure Virtual Networks (VNets) and their subnets.
    
    By default, generates an HTML report and opens it in the browser.
    This tool works with both Global Azure and China Azure environments.
    
    Examples:
        cloudqx azure vnet                      # Generate VNet HTML report (auto-detect cloud)
        cloudqx azure vnet --debug              # Generate VNet report with debug logging
        cloudqx azure vnet --cloud global       # Force use of Global Azure
        cloudqx azure vnet --cloud china        # Force use of China Azure
        cloudqx azure vnet --web               # Generate report and launch in browser
    """
    try:
        # Validate cloud parameter
        if cloud is not None and (not cloud or cloud.lower() not in ['global', 'china']):
            typer.echo(f"Invalid cloud environment '{cloud}'. Must be 'global' or 'china'.")
            raise typer.Exit(1)
        
        # Run the Azure vnet script as a subprocess
        
        # Build command (invoke the vnet module directly to avoid routing issues)
        cmd = [sys.executable, "providers/azure/vnet.py"]
        
        if debug:
            cmd.append("--debug")
        if cloud:
            cmd.extend(["--cloud", cloud.lower()])
        if web:
            cmd.append("--web")
        
        # Run the command with proper error handling
        try:
            result = subprocess.run(
                cmd, 
                cwd=str(project_root),
                check=False,  # Don't raise exception on non-zero exit
                capture_output=False  # Let subprocess output go directly to terminal
            )
            
            # Exit with the same code as the subprocess
            if result.returncode != 0:
                raise typer.Exit(result.returncode)
                
        except KeyboardInterrupt:
            typer.echo("Operation cancelled by user.", err=True)
            raise typer.Exit(1)
        except subprocess.SubprocessError as e:
            typer.echo(f"Error running Azure vnet command: {str(e)}", err=True)
            raise typer.Exit(1)
        except FileNotFoundError:
            typer.echo(
                f"Error: Azure vnet script not found at {project_root / 'providers' / 'azure' / 'vnet.py'}",
                err=True
            )
            raise typer.Exit(1)
            
    except KeyboardInterrupt:
        typer.echo("Operation cancelled by user.", err=True)
        raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"Error: {str(e)}", err=True)
        if debug:
            import traceback
            typer.echo(f"Traceback: {traceback.format_exc()}", err=True)
        raise typer.Exit(1)
    
@azure_app.command("dns-private")
def azure_dns_private(
    debug: Annotated[
        bool,
        typer.Option("--debug", "-d", help="Enable debug logging to show detailed debugging information")
    ] = False,
    cloud: Annotated[
        Optional[str],
        typer.Option("--cloud", "-c", help="Specify Azure cloud environment (auto-detects if not specified)", case_sensitive=False)
    ] = None,
    web: Annotated[
        bool,
        typer.Option("--web", "-w", help="Serve HTML report via web server (default: off)")
    ] = False,
):
    """
    Retrieve and report Azure Private DNS Zones, Virtual Network Links, Private Endpoints, Private Resolvers, and DNS Forwarding Rules.
    Generates an HTML table report displayed in a single page.
    """
    try:
        if cloud is not None and (not cloud or cloud.lower() not in ['global', 'china']):
            typer.echo(f"Invalid cloud environment '{cloud}'. Must be 'global' or 'china'.")
            raise typer.Exit(1)
        cmd = [sys.executable, "-m", "providers.azure.dns_private"]
        if debug:
            cmd.append("--debug")
        if cloud:
            cmd.extend(["--cloud", cloud.lower()])
        if web:
            cmd.append("--web")
        try:
            result = subprocess.run(cmd, cwd=str(project_root), check=False)
            if result.returncode != 0:
                raise typer.Exit(result.returncode)
        except KeyboardInterrupt:
            typer.echo("Operation cancelled by user.", err=True)
            raise typer.Exit(1)
    except KeyboardInterrupt:
        typer.echo("Operation cancelled by user.", err=True)
        raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"Error: {str(e)}", err=True)
        if debug:
            import traceback; typer.echo(f"Traceback: {traceback.format_exc()}", err=True)
        raise typer.Exit(1)


@azure_app.command("resource-overview")
def azure_resource_overview(
    debug: Annotated[
        bool,
        typer.Option("--debug", "-d", help="Enable debug logging to show detailed debugging information")
    ] = False,
    cloud: Annotated[
        Optional[str],
        typer.Option("--cloud", "-c", help="Specify Azure cloud environment (auto-detects if not specified)", case_sensitive=False)
    ] = None,
    web: Annotated[
        bool,
        typer.Option("--web", "-w", help="Serve HTML report via web server (default: off)")
    ] = False,
):
    """
    Comprehensive overview of all Azure resources across subscriptions.
    
    Generates a hierarchical report organized by subscription → resource group → resource type → individual resources.
    Uses Azure Resource Graph API to efficiently retrieve resource information from all accessible subscriptions.
    
    Examples:
        cloudqx azure resource-overview                     # Generate overview report (auto-detect cloud)
        cloudqx azure resource-overview --debug             # Generate with debug logging
        cloudqx azure resource-overview --cloud global      # Force use of Global Azure
        cloudqx azure resource-overview --web               # Launch web server and open in browser
    """
    try:
        if cloud is not None and (not cloud or cloud.lower() not in ['global', 'china']):
            typer.echo(f"Invalid cloud environment '{cloud}'. Must be 'global' or 'china'.")
            raise typer.Exit(1)
        
        cmd = [sys.executable, "-m", "providers.azure.resource_overview"]
        if debug:
            cmd.append("--debug")
        if cloud:
            cmd.extend(["--cloud", cloud.lower()])
        if web:
            cmd.append("--web")
        
        try:
            result = subprocess.run(cmd, cwd=str(project_root), check=False)
            if result.returncode != 0:
                raise typer.Exit(result.returncode)
        except KeyboardInterrupt:
            typer.echo("Operation cancelled by user.", err=True)
            raise typer.Exit(1)
    except KeyboardInterrupt:
        typer.echo("Operation cancelled by user.", err=True)
        raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"Error: {str(e)}", err=True)
        if debug:
            import traceback; typer.echo(f"Traceback: {traceback.format_exc()}", err=True)
        raise typer.Exit(1)


# AWS Commands (Placeholder)
@aws_app.command("info")
def aws_info():
    """
    Show AWS provider information and available commands.
    """
    typer.echo("🚧 AWS provider is coming soon!")
    typer.echo("")
    typer.echo("Planned features:")
    typer.echo("  • IAM user and role analysis")
    typer.echo("  • Resource inventory")
    typer.echo("  • Cost analysis")
    typer.echo("  • Security audit")
    typer.echo("")
    typer.echo("Stay tuned for updates!")


# GCP Commands (Placeholder)
@gcp_app.command("info")
def gcp_info():
    """
    Show GCP provider information and available commands.
    """
    typer.echo("🚧 Google Cloud Platform provider is coming soon!")
    typer.echo("")
    typer.echo("Planned features:")
    typer.echo("  • IAM analysis")
    typer.echo("  • Resource inventory")
    typer.echo("  • Cost analysis") 
    typer.echo("  • Security audit")
    typer.echo("")
    typer.echo("Stay tuned for updates!")


@app.command()
def version():
    """Show CloudQX version information."""
    typer.echo(f"CloudQX v{__version__} - Multi-Cloud Infrastructure Management Tool")
    typer.echo(f"Author: {__author__}")
    typer.echo("")
    typer.echo("Supported Cloud Providers:")
    typer.echo("  ✅ Azure - User management, RBAC analysis, resource discovery")
    typer.echo("  🚧 AWS - Coming soon")
    typer.echo("  🚧 GCP - Coming soon")
    typer.echo("")
    typer.echo("For help with specific commands, use: cloudqx [provider] --help")


def version_callback(value: bool):
    if value:
        typer.echo(f"CloudQX v{__version__} - Multi-Cloud Infrastructure Management Tool")
        raise typer.Exit()


@app.callback()
def main(
    ctx: typer.Context,
    version_flag: Annotated[
        Optional[bool],
        typer.Option(
            "--version", "-v",
            callback=version_callback,
            is_eager=True,
            help="Show version information and exit"
        )
    ] = None,
):
    """
    CloudQX - Multi-Cloud Infrastructure Management Tool
    
    A unified CLI tool for managing and analyzing cloud infrastructure
    across multiple cloud providers.
    
    [bold green]Currently supported providers:[/bold green]
    • [green]Azure[/green] - User management, RBAC analysis, resource discovery
    • [yellow]AWS[/yellow] - Coming soon
    • [yellow]GCP[/yellow] - Coming soon
    
    [bold blue]Quick Start:[/bold blue]
    • cloudqx azure user --help    # See Azure user management options
    • cloudqx --version             # Show version information
    
    Use --help with any command to see detailed usage information.
    """
    pass


@alibaba_app.command("multiaccountresources")
def alibaba_multiaccountresources(
    debug: bool = typer.Option(False, "--debug", "-d", help="Enable debug logging"),
    region: str = typer.Option(None, "--region", "-r", help="Specify region to filter resources (e.g. cn-beijing). If not set, fetch data for default regions: cn-beijing, cn-chengdu, cn-shenzhen, cn-hangzhou."),
    account: str = typer.Option(None, "--account", "-a", help="Filter resources by account name/display name (e.g. blz-p-ow-game). If not set, fetch data for all accounts."),
    resource_type: str = typer.Option(None, "--resource-type", "-t", help="Filter resources by resource type (e.g. ECS::Instance, RDS::Database). If not set, fetch data for all resource types.")
):
    """
    Check if Alibaba Multi-Account Resource Center service is enabled.
    """
    import sys
    import subprocess
    from pathlib import Path
    project_root = Path(__file__).parent
    cmd = [sys.executable, "-m", "providers.alibaba.multiaccountresources"]
    if debug:
        cmd.append("--debug")
    if region:
        cmd.extend(["--region", region])
    if account:
        cmd.extend(["--account", account])
    if resource_type:
        cmd.extend(["--resource-type", resource_type])
    try:
        result = subprocess.run(cmd, cwd=str(project_root), check=False)
        if result.returncode != 0:
            raise typer.Exit(result.returncode)
    except KeyboardInterrupt:
        typer.echo("Operation cancelled by user.", err=True)
        raise typer.Exit(1)
    except Exception as e:
        typer.echo(f"Error: {str(e)}", err=True)
        raise typer.Exit(1)

if __name__ == "__main__":
    app()

# Contains AI-generated edits.
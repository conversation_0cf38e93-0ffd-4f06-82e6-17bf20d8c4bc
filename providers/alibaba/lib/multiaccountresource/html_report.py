#!/usr/bin/env python3
"""
Alibaba Multi-Account Resource Center HTML report generator.
"""
import os
import json
from datetime import datetime
from typing import List, Dict, Any

def generate_multiaccountresource_html_report(
    hierarchical_data: List[Dict[str, Any]],
    stats: Dict[str, int]
) -> str:
    """
    Generate an interactive HTML report for Alibaba Multi-Account Resource Center.
    """
    html_dir = "html"
    os.makedirs(html_dir, exist_ok=True)

    multiaccountresource_dir = os.path.join(html_dir, "multiaccountresource")
    os.makedirs(multiaccountresource_dir, exist_ok=True)

    html_content = _generate_html_template(hierarchical_data, stats)

    output_path = os.path.join(html_dir, "alibaba_multiaccountresource_report.html")
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    return os.path.abspath(output_path)

def _generate_html_template(
    hierarchical_data: List[Dict[str, Any]],
    stats: Dict[str, Any]
) -> str:
    """
    Generate the complete HTML template for the Alibaba Multi-Account Resource Center.
    """
    import re
    from providers.alibaba.lib.multiaccountresource.data import group_resources_by_account_and_type
    # Transform flat resource list into hierarchical structure expected by JS
    grouped = group_resources_by_account_and_type(hierarchical_data)
    # Build list: account -> resource types -> resources
    hierarchical_list = []
    for account_name, types_dict in grouped.items():
        hierarchical_list.append({
            'account_id': re.sub(r'[^a-zA-Z0-9]', '_', account_name),
            'account_name': account_name,
            'resource_types': [
                {
                    'type': rt,
                    'resources': [
                        {**res,
                            'name': res.get('resource_id', '') or res.get('ResourceId', '') or res.get('resource_name', '') or res.get('ResourceName', 'Unknown'),
                            'type': res.get('resource_type', '') or res.get('ResourceType', ''),
                            'info': res.get('configuration', '')
                        }
                        for res in ress
                    ]
                }
                for rt, ress in types_dict.items()
            ]
        })
    data_json = json.dumps(hierarchical_list, indent=2, default=str)
    # Build stats payload with keys matching JS expectations
    stats_payload = {
        'total_resources': stats.get('TotalResources', 0),
        'total_accounts': stats.get('TotalAccounts', 0),
        'total_resource_types': sum(len(types) for types in grouped.values())
    }
    stats_json = json.dumps(stats_payload, indent=2, default=str)
    html_template = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alibaba Multi-Account Resource Center</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="resource_overview/resource_overview_report.css">
</head>
<body class="bg-orange-50 text-gray-900 font-sans">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-orange-600 shadow-lg border-b border-orange-700">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <h1 class="text-3xl font-bold text-white">
                            🏢 Alibaba Multi-Account Resource Center
                        </h1>
                    </div>
                    <div class="text-sm text-orange-100">
                        Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Summary Statistics -->
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-4">📊 Summary Statistics</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="summaryStats">
                    <!-- Stats populated by JavaScript -->
                </div>
            </div>

            <!-- Filter Controls -->
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-4">🔍 Filters</h2>
                <div class="bg-white p-6 rounded-lg shadow-lg border border-blue-200">
                    <div class="mb-4">
                        <label for="resourceSearch" class="block text-sm font-medium text-gray-700 mb-2">
                            Search Resources
                        </label>
                        <input type="text"
                               id="resourceSearch"
                               class="w-full border border-orange-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                               placeholder="Search resources by name, type, or ID...">
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="subscriptionFilter" class="block text-sm font-medium text-gray-700 mb-2">
                                Account
                            </label>
                            <select id="subscriptionFilter" class="w-full border border-orange-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                <option value="">All Accounts</option>
                            </select>
                        </div>
                        <div>
                            <label for="resourceGroupFilter" class="block text-sm font-medium text-gray-700 mb-2">
                                Resource Type
                            </label>
                            <select id="resourceGroupFilter" class="w-full border border-orange-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                <option value="">All Resource Types</option>
                            </select>
                        </div>
                        <div>
                            <label for="resourceTypeFilter" class="block text-sm font-medium text-gray-700 mb-2">
                                Resource
                            </label>
                            <select id="resourceTypeFilter" class="w-full border border-orange-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                <option value="">All Resources</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <button id="clearFilters" class="px-4 py-2 bg-orange-100 text-orange-700 rounded-md hover:bg-orange-200 transition-colors border border-orange-300 hover:border-orange-400">
                            Clear All Filters
                        </button>
                        <div class="text-sm text-gray-600">
                            <span id="filteredCount">0</span> resources displayed
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resource Hierarchy -->
            <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-semibold text-gray-900">🏗️ Resource Hierarchy</h2>
                    <div class="space-x-2">
                        <button id="expandAll" class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors border border-orange-600 hover:border-orange-700">
                            Expand All
                        </button>
                        <button id="collapseAll" class="px-4 py-2 bg-orange-100 text-orange-700 rounded-md hover:bg-orange-200 transition-colors border border-orange-300 hover:border-orange-400">
                            Collapse All
                        </button>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-lg border border-orange-200">
                    <div id="resourceHierarchy" class="p-6">
                        <!-- Hierarchy populated by JavaScript -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        window.multiAccountResourceData = {data_json};
        window.multiAccountResourceStats = {stats_json};
    </script>
    <script src="multiaccountresource/multiaccountresource_report.js"></script>
</body>
</html>"""
    return html_template